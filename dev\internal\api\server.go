package api

import (
	"git-rbi.jatismobile.com/poc/btn/dr-sms/internal/app"
	"git-rbi.jatismobile.com/poc/btn/dr-sms/pkg/log"
	"github.com/gin-gonic/gin"
)

type server struct {
	router *gin.Engine
	logger *log.LoggerEntry
	app    *app.App
}

func NewServer(router *gin.Engine, logger *log.LoggerEntry, app *app.App) *server {
	return &server{
		router: router,
		logger: logger,
		app:    app,
	}
}

func (s *server) Start() {
	s.logger.Info("Starting server at ", s.app.GetConfig().Server.BASEURL())
	err := s.router.Run(s.app.GetConfig().Server.BASEURL())
	if err != nil {
		s.logger.Error("", "Error starting server because", err)
	}
}

func (s *server) GetRouter() *gin.Engine {
	return s.router
}
