package app

import (
	"git-rbi.jatismobile.com/poc/btn/dr-sms/internal/api/handlers"
	"git-rbi.jatismobile.com/poc/btn/dr-sms/internal/pkg/config"
	"git-rbi.jatismobile.com/poc/btn/dr-sms/pkg/log"
)

type App struct {
	cfg                   *config.Config
	loggerProvider        *log.Provider
	authenticationHandler handlers.AuthenticationHandlerInterface
}

func NewApp(cfg *config.Config, loggerProvider *log.Provider) *App {
	return &App{
		cfg:            cfg,
		loggerProvider: loggerProvider,
	}
}

func (a *App) Start() {
	// Setup Database
	// mysqlProvider := mysql.NewProvider(a.cfg.DB.Mysql)

	// Setup Repositories
	// Setup Services
	// Setup Handlers

}

func (a *App) GetAuthenticationHandler() handlers.AuthenticationHandlerInterface {
	return a.authenticationHandler
}

func (a *App) GetConfig() *config.Config {
	return a.cfg
}
