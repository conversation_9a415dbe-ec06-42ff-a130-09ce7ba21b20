# If you prefer the allow list template instead of the deny list, see community template:
# https://github.com/github/gitignore/blob/main/community/Golang/Go.AllowList.gitignore
#
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
vendor/

# Go workspace file
go.work

config.docker.yaml

dev/main/config.local.yaml

docker.env

/app/

.idea

sonar.env

coverage.out
report.json
dev/coverage.out
dev/report.json

.scannerwork

/log/

config.*.yaml


temp/
