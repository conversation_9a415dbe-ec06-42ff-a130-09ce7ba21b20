# Project specification
sonar.host.url=${env.SONAR_HOST_URL}
sonar.login=${env.SONAR_LOGIN_TOKEN}
sonar.projectKey=${env.SONAR_PROJECT_KEY}
sonar.projectVersion=${env.SONAR_PROJECT_VERSION}

sonar.qualitygate.wait=true

# Analysis specification
sonar.language=go

# Testing specification
# sonar.go.tests.reportPaths=report.json
sonar.go.coverage.reportPaths=coverage.out
sonar.exclusions=**/mock/**,**/secret/**,**/docs/**,**/data/**,.idea/**,**/vendor/**,**/mocks/**
sonar.coverage.exclusions=**/tests/**,**/*_test.go,**/configuration/**
# sonar.test.inclusions=**/*_test.go
sonar.test.exclusions=**/vendor/**
# sonar.go.coverage.minimumCoverage=80
