FROM golang:1.22-alpine AS builder

WORKDIR /go/src/dev

RUN apk add --no-cache tzdata

COPY ./dev .

RUN CGO_ENABLED=0 GOOS=linux go build -o ../app/app ./main.go \
    && rm -rf /go/src/dev


FROM alpine:3.19 AS runtime

ARG APP_CONFIG=config.yaml

WORKDIR /go/src/app

COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo

COPY --from=builder /go/src/app/app ./app

COPY ${APP_CONFIG} config.yaml

EXPOSE 8080

CMD ["./app", "http", "--config", "config.yaml"]
