package mysql

import "fmt"

type Config struct {
	Provider              string `yaml:"provider"`
	Host                  string `yaml:"host"`
	Port                  int    `yaml:"port"`
	Database              string `yaml:"database"`
	User                  string `yaml:"user"`
	Password              string `yaml:"password"`
	MaxIdleConnection     int    `yaml:"max_idle_connection"`
	MaxOpenConnection     int    `yaml:"max_open_connection"`
	MaxLifetimeConnection int    `yaml:"max_lifetime_connection"`
	Table                 struct {
	} `yaml:"table"`
}

func (c Config) URI() string {
	uri := fmt.Sprintf("%v:%v@tcp(%v:%v)/%v?parseTime=true", c.User, c.Password, c.Host, c.Port, c.Database)
	uri += "&loc=Asia%2FJakarta"
	return uri
}
