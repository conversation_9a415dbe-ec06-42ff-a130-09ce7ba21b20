package http

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"git-rbi.jatismobile.com/poc/btn/dr-sms/internal/api"
	"git-rbi.jatismobile.com/poc/btn/dr-sms/internal/app"
	"git-rbi.jatismobile.com/poc/btn/dr-sms/internal/pkg/config"
	"git-rbi.jatismobile.com/poc/btn/dr-sms/pkg/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cobra"
)

var HttpCmd = &cobra.Command{
	Use:   "http",
	Short: "Run HTTP",
	Run: func(cmd *cobra.Command, args []string) {
		Start()
	},
}

var cfgFile string

func init() {
	HttpCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "config file (default is config.yaml)")
}

func Start() {
	msg := "Load config with path " + cfgFile
	fmt.Println(msg)

	// Setup cfg
	cfg := config.LoadConfig(cfgFile)
	ctx := context.Background()

	// Setup Log
	loggerProvider := log.NewProvider(&ctx, cfg.Log)

	loggerProvider.GetLogger("main").Info(msg)

	// Initialize Router
	router := gin.Default()

	// Run Server
	done := make(chan os.Signal, 1)
	signal.Notify(done, os.Interrupt, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		app := app.NewApp(cfg, loggerProvider)
		app.Start()
		server := api.NewServer(router, loggerProvider.GetLogger("server"), app)
		server.MapRoutes()
		server.Start()
	}()

	<-done

	loggerProvider.GetLogger("main").Info("Stopping server at ", cfg.Server.BASEURL())
}
