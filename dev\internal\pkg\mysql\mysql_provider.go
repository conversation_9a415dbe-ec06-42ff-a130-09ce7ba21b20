package mysql

import (
	"fmt"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
)

type Provider struct {
	db     *sqlx.DB
	config Config
}

func NewProvider(c Config) *Provider {
	db, err := sqlx.Open(c.Provider, c.<PERSON>RI())
	if err != nil {
		panic(fmt.Errorf("connect mysql database error caused by %+v", err))
	}
	fmt.Println("connect mysql database success")
	err = db.Ping()
	if err != nil {
		panic(fmt.Errorf("ping mysql database error caused by %+v", err))
	}
	fmt.Println("ping mysql database success")
	db.SetMaxIdleConns(c.MaxIdleConnection)
	db.SetMaxOpenConns(c.MaxOpenConnection)
	db.SetConnMaxLifetime(time.Duration(c.MaxLifetimeConnection) * time.Second)

	return &Provider{db: db, config: c}
}

func (p Provider) Database() *sqlx.DB {
	return p.db
}

func (p Provider) Close() {
	p.db.Close()
}
