package config

import (
	"git-rbi.jatismobile.com/poc/btn/dr-sms/internal/pkg/config/api"
	"git-rbi.jatismobile.com/poc/btn/dr-sms/internal/pkg/config/api/middlewares"
	"git-rbi.jatismobile.com/poc/btn/dr-sms/internal/pkg/config/server"
	"git-rbi.jatismobile.com/poc/btn/dr-sms/internal/pkg/mysql"
	"git-rbi.jatismobile.com/poc/btn/dr-sms/pkg/log"
	"gopkg.in/yaml.v3"

	"os"
)

type Config struct {
	Log log.Config `yaml:"log"`
	DB  struct {
		Mysql mysql.Config `yaml:"mysql"`
	} `yaml:"db"`
	Server server.Config          `yaml:"server"`
	Cors   middlewares.CorsConfig `yaml:"cors"`
	API    api.Config             `yaml:"api"`
}

func LoadConfig(configpath string) *Config {
	yfile, err1 := os.ReadFile(configpath)
	if err1 != nil {
		panic(err1)
	}
	config := Config{}
	err := yaml.Unmarshal(yfile, &config)
	if err != nil {
		panic(err)
	}
	return &config
}
