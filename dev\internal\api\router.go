package api

import (
	"net/http"

	"git-rbi.jatismobile.com/poc/btn/dr-sms/internal/pkg/config/api/middlewares"
	"git-rbi.jatismobile.com/poc/btn/dr-sms/internal/presentation"
	"github.com/gin-gonic/gin"
)

func (s *server) MapRoutes() {
	// Route Handling Error
	s.router.NoRoute(
		func(c *gin.Context) {
			errorResponse := presentation.ErrorResponse{
				Code:   http.StatusNotFound,
				Errors: []string{http.StatusText(http.StatusNotFound)},
			}
			c.J<PERSON>(http.StatusNotFound, errorResponse)
		},
	)
	s.router.NoMethod(
		func(c *gin.Context) {
			errorResponse := presentation.ErrorResponse{
				Code:   http.StatusMethodNotAllowed,
				Errors: []string{http.StatusText(http.StatusMethodNotAllowed)},
			}
			c.<PERSON>(http.StatusMethodNotAllowed, errorResponse)
		},
	)
	s.router.HandleMethodNotAllowed = true

	// CORS
	corsMiddleware := middlewares.CorsMiddleware(s.app.GetConfig().Cors)
	s.router.Use(corsMiddleware)

	apiWithCors := s.router.Group(s.app.GetConfig().API.Path)
	s.routesWithCors(apiWithCors)
}

func (s *server) routesWithCors(api *gin.RouterGroup) {
	authencationRoutes := api.Group(s.app.GetConfig().API.Child.Authentication.Path)
	{
		authencationRoutes.GET(s.app.GetConfig().API.Child.Authentication.Child.Login.Path, s.app.GetAuthenticationHandler().PostLogin)
	}
}
