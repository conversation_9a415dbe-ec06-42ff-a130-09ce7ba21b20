# DR SMS
## Clone This Project
- ##### SIT
    ```bash
    git clone --branch SIT https://git-rbi.jatismobile.com/poc/btn/dr-sms.git
    ```
- ##### Release (Production)
    ```bash
    git clone --branch Release https://git-rbi.jatismobile.com/poc/btn/dr-sms.git
    ```

## Overview


# How To Run App
Untuk menjalankan aplikasi baik ketika development ataupun production
## Developent
Command berikut terdiri dari beberapa command untuk melakukan development aplikasi:
- ##### Go Get
    ```bash
    make get github.com/gin-gonic/gin
    ```
- ##### Go Run
    ```bash
    make run
    ```
- ##### Go Test
    ```bash
    make test
    ```
- ##### Go Mod Tidy
    ```bash
    make tidy
    ```
## Docker Command
- ##### Version (Untuk check versi app)
    ```bash
    make version
    ```
- ##### Setup (Untuk pertama kali setelah clone)
    ```bash
    make setup
    ```
- ##### Update/Pull (Untuk update source app)
    ```bash
    make sync {empty/version}
    ```
- ##### Start/Restart (Untuk menjalankan aplikasi)
    ```bash
    make up
    ```
- ##### Stop (Untuk memberhentikan Aplikasi)
    ```bash
    make down
    ```
- ##### Log Docker (Untuk melihat log docker)
    ```bash
    make logs
    ```
- ##### Stats Docker (Untuk melihat stats docker)
    ```bash
    make stats
    ```
## For Deploy SIT/DEV
1. ##### For New Deploy
   - make setup
   - Update/Edit **config.docker.yaml**
   - Update/Edit docker.env
   - make up
   - make stats (Untuk mengecek statistic docker OPTIONAL)
2. ##### For Existing Deploy
   - make sync
   - Update/Edit **config.docker.yaml**
   - Update/Edit docker.env
   - make up
   - make stats (Untuk mengecek statistic docker OPTIONAL)
## For Deploy PRODUCTION
1. ##### For New Deploy
   - make setup
   - make sync v1.0.0
   - Update/Edit **config.docker.yaml**
   - Update/Edit docker.env
   - make up
   - make stats (Untuk mengecek statistic docker OPTIONAL)
2. ##### For Existing Deploy
   - make sync v2.0.0
   - Update/Edit **config.docker.yaml**
   - Update/Edit docker.env
   - make up
   - make stats (Untuk mengecek statistic docker OPTIONAL)