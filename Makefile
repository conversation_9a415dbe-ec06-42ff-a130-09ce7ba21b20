# Import docker Config
-include docker.env
export

# Extract the current directory name
APP_NAME := $(notdir $(patsubst %/,%,$(CURDIR)))
AFTER_FIRST_CMD = $(filter-out $@,$(MAKECMDGOALS))

# List Real Command
GET_CMD = go get $(AFTER_FIRST_CMD)
RUN_CMD = go run ./main.go http --config ../config.go.yaml
TEST_CMD = go test $$(go list ./... | grep -v **/mocks | grep -v **/configuration | grep -v **/tests) -count=1 -race -v -covermode=atomic -cover -coverprofile=../coverage.out
TIDY_CMD = go mod tidy
VERSION_CMD = git describe --tags
ifeq ($(filter-out sync,$(MAKECMDGOALS)),)
	SYNC_CMD = git pull
else 
	SYNC_CMD = git fetch --all --tags --prune && git checkout tags/$(AFTER_FIRST_CMD)
endif
SETUP_CMD = cp sample.config.docker.yaml config.docker.yaml && cp sample.config.docker.yaml config.go.yaml && cp sample.docker.env docker.env && cp sample.sonar.env sonar.env
DOCKER_UP_CMD = CONTAINER_NAME=$(APP_NAME)-app docker-compose --env-file docker.env up -d --build app
DOCKER_DOWN_CMD = CONTAINER_NAME=$(APP_NAME)-app docker-compose --env-file docker.env stop app
DOCKER_DESTROY_CMD = CONTAINER_NAME=$(APP_NAME)-app docker-compose --env-file docker.env down
DOCKER_LOGS_CMD = docker logs -f $(APP_NAME)-app
DOCKER_STATS_CMD = docker stats $(APP_NAME)-app
SONARQUBE_CMD = docker run --env-file sonar.env --rm -v .:/usr/src sonarsource/sonar-scanner-cli


ifeq ($(filter-out sync,$(MAKECMDGOALS)),)
	SYNC_CMD = git pull
else 
	SYNC_CMD = git fetch --all --tags --prune && git checkout tags/$(AFTER_FIRST_CMD)
endif

# Update Command If OS Windows
ifeq ($(OS),Windows_NT)
endif

.PHONY: get run test tidy version sync setup up down destroy logs stats dev sit prod sonar

get:
	@cd dev && ${GET_CMD}

run:
	@cd dev && $(RUN_CMD)

test:
	@cd dev && $(TEST_CMD)
	@sed -ri "s~$$(sed 1q dev/go.mod | cut -d' ' -f2)/~dev/~g" coverage.out

tidy:
	@cd dev && $(TIDY_CMD)

version:
	@${VERSION_CMD}

sync:
	@${SYNC_CMD}

setup:
	@$(SETUP_CMD)

up:
	@$(DOCKER_UP_CMD)

down:
	@$(DOCKER_DOWN_CMD)

logs:
	@$(DOCKER_LOGS_CMD)

stats:
	@$(DOCKER_STATS_CMD)

sonar: # test
	@$(SONARQUBE_CMD)